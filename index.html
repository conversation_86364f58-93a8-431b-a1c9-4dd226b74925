<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>'s - Flexbox Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: #333;
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }
        
        .header p {
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 30px;
            justify-content: center;
            flex-wrap: wrap;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background-color: white;
            padding: 25px;
            width: 300px;
            border-radius: 12px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .card:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .card img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2575fc;
        }
        
        .card p {
            line-height: 1.6;
            color: #555;
            flex-grow: 1;
        }
        
        .card-footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Media queries voor responsive design */
        @media (max-width: 768px) {
            .container {
                gap: 20px;
            }
            
            .card {
                width: 100%;
                max-width: 400px;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Wat ik graag doe</h1>
        <p>Dit zijn mijn favoriete hobby's</p>
    </div>
    
    <div class="container">
        <div class="card">
            <img src="https://via.placeholder.com/300x180/4CAF50/white?text=CODE" alt="Programmeren">
            <h3>Programmeren</h3>
            <p>Ik leer graag nieuwe dingen op de computer. Websites maken en apps bouwen is super leuk! Ik kan mijn eigen ideeën maken en problemen oplossen.</p>
            <div class="card-footer">Sinds 2018</div>
        </div>
        
        <div class="card">
            <img src="https://via.placeholder.com/300x180/FF9800/white?text=MOUNTAIN" alt="Bergbeklimmen">
            <h3>Bergbeklimmen</h3>
            <p>Ik hou van buiten zijn en hoge bergen beklimmen. Het is spannend en je ziet prachtige uitzichten! Het is ook goed voor mijn conditie.</p>
            <div class="card-footer">Sinds 2015</div>
        </div>
        
        <div class="card">
            <img src="https://via.placeholder.com/300x180/2196F3/white?text=CAMERA" alt="Fotografie">
            <h3>Fotografie</h3>
            <p>Ik maak graag foto's van mooie dingen die ik zie. Natuur, mensen, gebouwen - alles kan interessant zijn! Mijn camera gaat altijd mee op reis.</p>
            <div class="card-footer">Sinds 2016</div>
        </div>
    </div>
</body>
</html>